package com.yd.dcap.probe.client;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskConfig {
    private Long taskId;
    private Long currentVersion=0L;
    private Long scanJobHistoryId;
    private Long tenantId;
    private String name;
    private TaskParam taskParam;
    private Map<String,String> hashTables;
    private DataSource datasource;
    private List<Policy> policies;
    private List<SensDataMarking> sensDataMarkings;
    private String dataDictionary;



    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskParam {

        // 是否排除空值
        private Boolean excludeEmptyValues;

        // 是否开启增量识别，默认开启
        private Boolean incrementalEvaluationEnabled;

        // 开启数据倒序采样
        private Boolean samplingReverseOrder;

        private int permitsPerSecond = 1000;

        // 空值达到多少百分比，就进行二次采样。默认 98%
        private Integer emptyPercentage;
        // 是否随机采样
        private boolean useRandomSampling = false;
        // 是否开启对视图定义进行采样
        private boolean viewDefinitionSampling;
        // 采样数量
        private int sampleCount;
        //  采样 sql 执行超时时间  秒
        private int sampleSqlTimeout;

        private boolean tableRowCountEnabled;

        private int tableRowCount;

        private int tableRowCountUnit;

        private boolean tableCapacityEnabled;

        // ColumnData 和 ColumnName ============ start ==========
        // 需要排除匹配的 schema
        private String excludeSchema;
        // 排除匹配的表
        private String excludeTable;
        // 排除匹配的列
        private String excludeColumn;
        // tableName 模糊查询
        private String tableNameLike;

        // 查找的表类型，支持 TABLE,VIEW,SYNONYM
        private String tableType;
        // ColumnData 和 ColumnName  ============ end ==========

        // 只有 ColumnData 有效 ============ start ==========
        // columnName 模糊查询
        private String columnNameLike;
        // 列值的近似匹配，就是在模式匹配之前的过滤。可以让用户来决定那些值才会执行 模式匹配
        private String searchValueLike;
        // 数据值的最小长度，低于这个长度就忽略不进行匹配，默认 16
        private int minimumLength;
        // 数据值的最大长度，超出这个长度就忽略不进行匹配，默认 255
        private int maximumLength;

        // 识别命中率
        private int hitPercentage;

        // 数据类型 取值 "NUMBER,TEXT"
        private String dataType;
        // 只有 ColumnData 有效 ============ end ==========

        private ScanRange scanRange;

        /**
         * 自动打标的阈值。默认是 60 当前是 DSPM 才会传递
         */
        private Integer markThresholds=60;

        // 是否开启智能血缘识别，默认开启
        private Boolean intelligentLineageEnabled = true;

        // 是否开启智能指纹识别，默认开启
        private Boolean intelligentFingerprintEnabled = true;

        public int getTableRowCountLimit(){
            long rowCountValue = this.tableRowCount;
            switch (this.tableRowCountUnit){
                case 2:
                    rowCountValue = rowCountValue * 10000;
                    break;
                case 3:
                    rowCountValue = rowCountValue * 100000;
                    break;
                case 4:
                    rowCountValue = rowCountValue * 1000000;
                    break;
                case 5:
                    rowCountValue = rowCountValue * 10000000;
                    break;
                case 1:
                default:
                    break;
            }
            // 如果太大，就设置数量为采样数 + 1
            if(rowCountValue > Integer.MAX_VALUE){
                rowCountValue = this.sampleCount + 1;
            }
            return (int) rowCountValue;
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        public static class ScanRange{
            private Set<String> selectedSchema = new HashSet<>();
            private Set<String> selectedDatabase = new HashSet<>();
            private Set<String> selectedTable = new HashSet<>();
            private Set<String> selectedView = new HashSet<>();
            private Set<String> selectedSynonym = new HashSet<>();

            // 检查的逻辑应该是先进行排除，再进行添加。
            private Set<String> excludedDatabase = new HashSet<>();
            private Set<String> excludedSchema = new HashSet<>();
            private Set<String> excludedTable = new HashSet<>();
            private Set<String> excludedView = new HashSet<>();
            private Set<String> excludedSynonym = new HashSet<>();

            public Set<String> getSelectedSchema() {
                return selectedSchema.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }

            public Set<String> getSelectedDatabase() {
                return selectedDatabase.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }

            public Set<String> getSelectedTable() {
                return selectedTable.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }

            public Set<String> getSelectedView() {
                return selectedView.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }

            public Set<String> getSelectedSynonym() {
                return selectedSynonym.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }

            public Set<String> getExcludedDatabase() {
                return excludedDatabase.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }

            public Set<String> getExcludedSchema() {
                return excludedSchema.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }

            public Set<String> getExcludedTable() {
                return excludedTable.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }

            public Set<String> getExcludedView() {
                return excludedView.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }

            public Set<String> getExcludedSynonym() {
                return excludedSynonym.stream().map(String::toLowerCase).collect(Collectors.toSet());
            }
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DataSource {
        private String id;
        private String name;
        private String sourceType;
        private String host;
        private String port;
        private AuthCfg authCfg;
        private String extraCfg;
        private Integer dbNameType;
        private Integer encryptionSwitch;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AuthCfg {
        private String username;
        private String password;
        private String authMethod;
        private String krb5conf;
        private String servicePrincipal;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Policy {
        private String id;
        private String name;
        private String builtin;
        private Long category;
        private String dataTag;
        private String dataTagTypeForDspm = "BASIC";
        private int dataTagOrderBy=50;
        private Long level;
        private String levelName;
        private List<Pattern> patterns;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Pattern {
        private String type;
        private String expr;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Model {
        private String type;
        private String expr;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SensDataMarking {
        private String datasourceId;
        private String qualifiedName;
        private String deletedAutoTags;
        private String manualTags;
        private Long tenantId;
    }
}
